'use client'

import { useEffect, useMemo, useState } from 'react'
import {
  ButtonUrl,
  cn,
  CountdownTimer,
  CurrentComponents,
  HomeCoupons,
  HomePageComponents,
  resolveCatchMessage,
  TCatchMessage,
  TRACK_EVENT,
  useCurrentTime,
  useGetHomePageConfigQuery,
  useScrollToTop,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'

import {
  Banner,
  CustomEmpty,
  Discount,
  ImagePreloader,
  MiniBannerLazy,
  ModuleHeader,
  ReceiveCouponListLazy,
  RecommendProductsLazy,
  ScrollToTopButton,
  Skeleton,
  Specialized,
} from '@/components'
import { useTabContext } from '@/components/business/marketing/TabContext'
import { formatComponents } from '@/utils/format'

/**
 * 首页
 */
const HomePage = () => {
  const { activeTab } = useTabContext()
  const { timestamp: currentTime, fetchCurrentTime } = useCurrentTime()
  const [homeComponents, setHomeComponents] = useState<HomePageComponents>([])
  const toast = useToastContext()
  const [isLoading, setIsLoading] = useState(true)
  const { reportEvent } = useVolcAnalytics()
  const { currentData: homePageInfo, error } = useGetHomePageConfigQuery(
    {
      identifier: activeTab,
      customAttributesV3Filter: {
        filter_attributes: [
          'product_tag',
          'paymeng_method',
          'max_usage_limit_ncoins',
          'is_insurance',
          'insurance_link',
        ],
      },
    },
    {
      skip: !activeTab,
    },
  )

  useEffect(() => {
    if (homePageInfo) {
      const components = homePageInfo.pageComponents?.components || []
      setHomeComponents(components)
      // 移除不必要的setTimeout，直接设置loading状态
      setIsLoading(false)
    }
  }, [homePageInfo])

  useEffect(() => {
    fetchCurrentTime()
  }, [fetchCurrentTime])

  useEffect(() => {
    if (error) {
      toast.show({
        icon: 'fail',
        content: resolveCatchMessage(error as TCatchMessage) as string,
      })
    }
  }, [error, toast])

  /**
   * 埋点：打开商城
   */
  useEffect(() => {
    reportEvent(TRACK_EVENT.shop_home_page_exposure, {
      store_name: activeTab,
    })
  }, [activeTab, reportEvent])

  /**
   * 使用滚动到顶部Hook
   */
  useScrollToTop({
    trigger: 'both',
    scrollOnMount: true,
    behavior: 'smooth',
  })

  /**
   * 格式化模块数据
   */
  const currentHomeComponents: CurrentComponents[] = useMemo(() => {
    if (activeTab) {
      if (homeComponents?.length) {
        return formatComponents(homeComponents, currentTime)
      }

      return []
    }

    return []
  }, [homeComponents, currentTime, activeTab])

  /**
   * 提取关键图片用于预加载（LCP优化）
   */
  const criticalImages = useMemo(() => {
    const images: string[] = []

    // 提取Banner图片（通常是LCP元素）
    currentHomeComponents.forEach((component) => {
      if (component.model_type === 2 && component.relation_items) {
        // Banner轮播图片
        component.relation_items.slice(0, 2).forEach((item) => {
          if (item?.image_url_desktop) {
            images.push(item.image_url_desktop)
          }
        })
      }
    })

    return images
  }, [currentHomeComponents])

  const renderModule = (item: CurrentComponents) => {
    const {
      id,
      title,
      products = [],
      activity_end_at: countdownEndTime,
      relation_items: relationItems = [],
      showHorizonModule,
      coupon_rules: couponList = [],
      model_type: modelType, // 模块类型：1:图片和产品,2:图片轮播,3:图片瀑布流,4:固定图标,5:领券
    } = item

    switch (modelType) {
      case 1:
        return showHorizonModule ? (
          <Specialized
            banner={relationItems && relationItems[0]}
            products={products}
            currentTime={countdownEndTime || ''}
          />
        ) : (
          <Discount products={products} currentTime={countdownEndTime || ''} />
        )

      case 2:
        return <Banner bannerData={relationItems} />

      case 3:
        return <MiniBannerLazy isHome={true} items={relationItems} />

      case 4:
        return null

      case 5:
        return (
          <ReceiveCouponListLazy
            title={title || ''}
            couponList={couponList as HomeCoupons}
            componentId={id as number}
          />
        )
    }
  }

  return (
    <>
      {/* 预加载关键图片以优化LCP */}
      <ImagePreloader images={criticalImages} priority={true} />

      {isLoading ? (
        <div className="max-container rounded-[20px]">
          <Skeleton style={{ marginTop: 56, marginBottom: 48, height: 400 }} />
          <div className="flex flex-row gap-[16px]">
            {Array.from({ length: 3 }).map((_, position) => (
              <Skeleton key={position} style={{ marginBottom: 48, height: 188, width: '33.3%' }} />
            ))}
          </div>
        </div>
      ) : currentHomeComponents?.length > 0 ? (
        currentHomeComponents.map((item, position) => {
          const {
            title,
            button_text: buttonText,
            is_display: isModuleHeaderDisplay,
            activity_start_at: countdownStartTime,
            activity_end_at: countdownEndTime,
            relation_items: relationItems = [],
            url,
            hasCountdown,
            model_type: modelType, // 模块类型：1:图片和产品,2:图片轮播,3:图片瀑布流,4:固定图标,5:领券
          } = item

          if (
            (modelType === 1 && item?.products?.length === 0) ||
            ([2, 3, 4].includes(modelType || 0) && relationItems?.length === 0) ||
            (modelType === 5 && item?.coupon_rules?.length === 0) ||
            modelType === 6 // 排行榜模块
          ) {
            return null
          }

          const customStyle = () => {
            switch (modelType) {
              case 1:
                return 'mb-[88px]'
              case 2:
                return 'mb-[52px] xll:mb-[64px]'
              case 3:
                return 'mb-[88px]'
              case 4:
                return ''
              case 5:
                return 'mb-[88px]'
            }
          }

          return (
            <div
              key={item.id}
              className={cn([
                modelType !== 2 && modelType !== 1 ? 'max-container-no-mb' : '',
                customStyle(),
              ])}>
              {![2, 4, 5].includes(modelType || 0) && isModuleHeaderDisplay && (
                <div className={modelType !== 2 ? 'max-container-no-mb' : ''}>
                  <ModuleHeader
                    title={title || ''}
                    buttonText={buttonText || ''}
                    url={url as ButtonUrl}
                    timeCounter={
                      ([1, 5].includes(modelType || 0) ||
                        (position === 0 && relationItems?.length)) &&
                      hasCountdown ? (
                        <CountdownTimer
                          startTime={currentTime}
                          activityStartTime={countdownStartTime || ''}
                          targetTime={countdownEndTime as string}
                          type="horizontal_day"
                          textStyle="text-[18px] font-miSansSemibold520 text-[#000000]"
                          timeBoxStyle="bg-[#DA291C]"
                          timeTextStyle="text-[#FFFFFF]"
                          colonStyle="text-[#BBBBBD]"
                        />
                      ) : null
                    }
                  />
                </div>
              )}
              {renderModule(item)}
            </div>
          )
        })
      ) : (
        <div className="max-container-no-mb mb-48 mt-[128px]">
          <CustomEmpty />
          <RecommendProductsLazy />
        </div>
      )}

      {/* 返回顶部按钮 */}
      <ScrollToTopButton position="two-thirds" />
    </>
  )
}

export default HomePage
