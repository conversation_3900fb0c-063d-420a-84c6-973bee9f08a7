'use client'

import { useEffect } from 'react'
import Head from 'next/head'
import { RelationItem } from '@ninebot/core'

interface BannerPreloaderProps {
  bannerData: RelationItem[] | null
}

/**
 * Banner图片预加载组件 - 专门优化LCP性能
 * 激进地预加载前2张Banner图片以最大化LCP优化
 */
const BannerPreloader = ({ bannerData }: BannerPreloaderProps) => {
  const criticalBannerImages = bannerData
    ?.filter((item) => item?.button_url?.type !== 'seckill')
    .filter((item) => !!item?.image_url_desktop)
    .slice(0, 2) // 只预加载前2张最关键的图片
    .map((item) => item.image_url_desktop)
    .filter(Boolean) || []

  useEffect(() => {
    // 客户端激进预加载策略
    if (typeof window !== 'undefined' && criticalBannerImages.length > 0) {
      criticalBannerImages.forEach((src, index) => {
        if (src) {
          // 创建预加载链接
          const link = document.createElement('link')
          link.rel = 'preload'
          link.as = 'image'
          link.href = src
          link.fetchPriority = index === 0 ? 'high' : 'auto'
          
          // 添加到head
          document.head.appendChild(link)

          // 同时创建Image对象进行预加载
          const img = new Image()
          img.src = src
          
          // 第一张图片使用最高优先级
          if (index === 0) {
            img.loading = 'eager'
            img.fetchPriority = 'high'
          }
        }
      })
    }
  }, [criticalBannerImages])

  if (criticalBannerImages.length === 0) {
    return null
  }

  return (
    <Head>
      {criticalBannerImages.map((src, index) => (
        src && (
          <link
            key={`banner-preload-${index}`}
            rel="preload"
            as="image"
            href={src}
            fetchPriority={index === 0 ? 'high' : 'auto'}
          />
        )
      ))}
    </Head>
  )
}

export default BannerPreloader
