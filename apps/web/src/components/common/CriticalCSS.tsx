'use client'

/**
 * 关键CSS内联组件 - 优化首屏渲染
 * 内联Banner组件的关键样式以减少渲染阻塞
 */
const CriticalCSS = () => {
  return (
    <style jsx>{`
      /* Banner关键样式 - 内联以优化LCP */
      .banner-container {
        position: relative;
        width: 100%;
        max-width: 2560px;
        margin: 0 auto;
        overflow: hidden;
      }
      
      .banner-image {
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center center;
      }
      
      .banner-content {
        position: relative;
        z-index: 20;
        color: white;
        text-align: center;
      }
      
      /* 响应式Banner高度 */
      @media (min-width: 1024px) {
        .banner-container {
          height: clamp(424px, calc(424px + (597 - 424) * ((100vw - 1024px) / (1440 - 1024))), 597px);
        }
      }
      
      @media (min-width: 1440px) {
        .banner-container {
          height: clamp(597px, calc(597px + (800 - 597) * ((100vw - 1440px) / (1920 - 1440))), 800px);
        }
      }
      
      @media (min-width: 1920px) {
        .banner-container {
          height: 800px;
        }
      }
      
      /* 预加载动画优化 */
      .banner-image[data-loading="true"] {
        background-color: #f0f0f0;
        background-image: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }
      
      @keyframes loading {
        0% {
          background-position: 200% 0;
        }
        100% {
          background-position: -200% 0;
        }
      }
    `}</style>
  )
}

export default CriticalCSS
