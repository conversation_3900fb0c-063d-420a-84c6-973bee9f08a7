'use client'

import { useEffect } from 'react'
import Head from 'next/head'

interface ImagePreloaderProps {
  images: string[]
  priority?: boolean
}

/**
 * 图片预加载组件 - 用于优化LCP性能
 * 通过预加载关键图片资源来减少LCP时间
 */
const ImagePreloader = ({ images, priority = true }: ImagePreloaderProps) => {
  useEffect(() => {
    // 客户端预加载逻辑
    if (typeof window !== 'undefined' && images.length > 0) {
      images.forEach((src) => {
        if (src) {
          const link = document.createElement('link')
          link.rel = 'preload'
          link.as = 'image'
          link.href = src
          // 添加现代图片格式支持
          link.type = 'image/webp'
          document.head.appendChild(link)
        }
      })
    }
  }, [images])

  if (!priority || images.length === 0) {
    return null
  }

  return (
    <Head>
      {images.map((src, index) => (
        src && (
          <link
            key={`preload-${index}`}
            rel="preload"
            as="image"
            href={src}
            type="image/webp"
          />
        )
      ))}
    </Head>
  )
}

export default ImagePreloader
