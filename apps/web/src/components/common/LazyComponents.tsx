'use client'

import { lazy, Suspense } from 'react'
import { HomeCoupons, RelationItems } from '@ninebot/core'

import { Skeleton } from '@/components'

// 懒加载非关键组件以减少初始bundle大小
const LazyRecommendProducts = lazy(() =>
  import('@/components/business/product/recommendedProducts').then((module) => ({
    default: module.RecommendProducts,
  })),
)

const LazyReceiveCouponList = lazy(() =>
  import('@/components/common/receiveCouponList').then((module) => ({
    default: module.ReceiveCouponList,
  })),
)

const LazyMiniBanner = lazy(() => import('@/components/business/marketing/Banner/MiniBanner'))

// 类型定义
type RecommendProductsProps = {
  sku?: string
  isCartPage?: boolean
  customPageSize?: number
  headerTitleStyle?: string
  productItemContainerStyle?: React.CSSProperties | undefined
  isProductDetailPage?: boolean
  productNameWrapperStyle?: string
  isSearchModal?: boolean
  isSearchPage?: boolean
  closeModal?: () => void
  rootContainerClass?: string
}

type ReceiveCouponListProps = {
  title?: string
  description?: string
  couponList: HomeCoupons
  containerStyle?: string
  componentId: number
  isRefresh?: boolean
}

type MiniBannerProps = {
  isHome?: boolean
  items: RelationItems | null
}

// 懒加载包装器组件
export const RecommendProductsLazy = (props: RecommendProductsProps) => (
  <Suspense fallback={<Skeleton style={{ height: 200, marginBottom: 24 }} />}>
    <LazyRecommendProducts {...props} />
  </Suspense>
)

export const ReceiveCouponListLazy = (props: ReceiveCouponListProps) => (
  <Suspense fallback={<Skeleton style={{ height: 120, marginBottom: 24 }} />}>
    <LazyReceiveCouponList {...props} />
  </Suspense>
)

export const MiniBannerLazy = (props: MiniBannerProps) => (
  <Suspense fallback={<Skeleton style={{ height: 160, marginBottom: 24 }} />}>
    <LazyMiniBanner {...props} />
  </Suspense>
)
