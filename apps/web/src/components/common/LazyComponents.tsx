'use client'

import { lazy, Suspense } from 'react'
import { Skeleton } from '@/components'

// 懒加载非关键组件以减少初始bundle大小
const LazyRecommendProducts = lazy(() => import('@/components/business/product/RecommendProducts'))
const LazyReceiveCouponList = lazy(() => import('@/components/common/receiveCouponList'))
const LazyMiniBanner = lazy(() => import('@/components/business/marketing/Banner/MiniBanner'))

// 懒加载包装器组件
export const RecommendProductsLazy = (props: any) => (
  <Suspense fallback={<Skeleton style={{ height: 200, marginBottom: 24 }} />}>
    <LazyRecommendProducts {...props} />
  </Suspense>
)

export const ReceiveCouponListLazy = (props: any) => (
  <Suspense fallback={<Skeleton style={{ height: 120, marginBottom: 24 }} />}>
    <LazyReceiveCouponList {...props} />
  </Suspense>
)

export const MiniBannerLazy = (props: any) => (
  <Suspense fallback={<Skeleton style={{ height: 160, marginBottom: 24 }} />}>
    <LazyMiniBanner {...props} />
  </Suspense>
)
